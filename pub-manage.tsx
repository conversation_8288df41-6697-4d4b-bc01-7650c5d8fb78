import React, { useState } from 'react';
import {
  Search, Plus, Edit, Trash2, Save, X, ChevronDown, ChevronRight, Setting<PERSON>, Clock, Utensils
} from 'lucide-react';

// 明细项类型定义
interface DetailItem {
  id: string;
  name: string;
  value: string;
  sort: number;
  status: '启用' | '禁用';
  remark?: string;
}

// 项目类型定义
interface ProjectItem {
  id: string;
  name: string;
  code: string;
  type: '选项类' | '时间类' | '数值类';
  description: string;
  status: '启用' | '禁用';
  createTime: string;
  details: DetailItem[];
}

// Mock数据
const mockProjectData: ProjectItem[] = [
  {
    id: 'P001',
    name: '餐次管理',
    code: 'MEAL_TIME',
    type: '选项类',
    description: '定义一日三餐的餐次类型',
    status: '启用',
    createTime: '2024-01-01',
    details: [
      { id: 'D001', name: '早餐', value: 'breakfast', sort: 1, status: '启用', remark: '早上用餐' },
      { id: 'D002', name: '中餐', value: 'lunch', sort: 2, status: '启用', remark: '中午用餐' },
      { id: 'D003', name: '晚餐', value: 'dinner', sort: 3, status: '启用', remark: '晚上用餐' }
    ]
  },
  {
    id: 'P002',
    name: '膳食分类',
    code: 'DIET_TYPE',
    type: '选项类',
    description: '定义不同类型的膳食分类',
    status: '启用',
    createTime: '2024-01-01',
    details: [
      { id: 'D004', name: '普通餐', value: 'normal', sort: 1, status: '启用', remark: '正常饮食' },
      { id: 'D005', name: '治疗餐', value: 'therapeutic', sort: 2, status: '启用', remark: '特殊治疗饮食' },
      { id: 'D006', name: '流质', value: 'liquid', sort: 3, status: '启用', remark: '流质饮食' },
      { id: 'D007', name: '半流质', value: 'semi_liquid', sort: 4, status: '启用', remark: '半流质饮食' }
    ]
  },
  {
    id: 'P003',
    name: '订餐截止时间',
    code: 'ORDER_DEADLINE',
    type: '时间类',
    description: '定义各餐次的订餐截止时间',
    status: '启用',
    createTime: '2024-01-01',
    details: [
      { id: 'D008', name: '早餐截止时间', value: '20:00', sort: 1, status: '启用', remark: '前一天晚上8点截止' },
      { id: 'D009', name: '中餐截止时间', value: '09:30', sort: 2, status: '启用', remark: '当天上午9点30分截止' },
      { id: 'D010', name: '晚餐截止时间', value: '14:00', sort: 3, status: '启用', remark: '当天下午2点截止' }
    ]
  }
];

interface PubManageProps {
  darkMode?: boolean;
}

function PubManage({ darkMode = false }: PubManageProps) {
  // 状态管理
  const [projects, setProjects] = useState<ProjectItem[]>(mockProjectData);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('全部');
  const [selectedStatus, setSelectedStatus] = useState('全部');
  const [expandedProjects, setExpandedProjects] = useState<string[]>(['P001']);

  // 模态框状态
  const [showProjectModal, setShowProjectModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingProject, setEditingProject] = useState<ProjectItem | null>(null);
  const [editingDetail, setEditingDetail] = useState<DetailItem | null>(null);
  const [currentProjectId, setCurrentProjectId] = useState<string>('');
  const [deleteTarget, setDeleteTarget] = useState<{ type: 'project' | 'detail', id: string, projectId?: string } | null>(null);

  // 表单数据
  const [projectForm, setProjectForm] = useState({
    name: '',
    code: '',
    type: '选项类' as '选项类' | '时间类' | '数值类',
    description: '',
    status: '启用' as '启用' | '禁用'
  });

  const [detailForm, setDetailForm] = useState({
    name: '',
    value: '',
    sort: 1,
    status: '启用' as '启用' | '禁用',
    remark: ''
  });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // 过滤数据
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === '全部' || project.type === selectedType;
    const matchesStatus = selectedStatus === '全部' || project.status === selectedStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  // 分页数据
  const totalPages = Math.ceil(filteredProjects.length / pageSize);
  const paginatedData = filteredProjects.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // 展开/收起项目
  const toggleProject = (projectId: string) => {
    setExpandedProjects(prev =>
      prev.includes(projectId)
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    );
  };

  // 项目操作
  const handleAddProject = () => {
    setEditingProject(null);
    setProjectForm({
      name: '',
      code: '',
      type: '选项类',
      description: '',
      status: '启用'
    });
    setShowProjectModal(true);
  };

  const handleEditProject = (project: ProjectItem) => {
    setEditingProject(project);
    setProjectForm({
      name: project.name,
      code: project.code,
      type: project.type,
      description: project.description,
      status: project.status
    });
    setShowProjectModal(true);
  };

  const handleDeleteProject = (projectId: string) => {
    setDeleteTarget({ type: 'project', id: projectId });
    setShowDeleteModal(true);
  };

  // 明细操作
  const handleAddDetail = (projectId: string) => {
    setCurrentProjectId(projectId);
    setEditingDetail(null);
    setDetailForm({
      name: '',
      value: '',
      sort: 1,
      status: '启用',
      remark: ''
    });
    setShowDetailModal(true);
  };

  const handleEditDetail = (detail: DetailItem, projectId: string) => {
    setCurrentProjectId(projectId);
    setEditingDetail(detail);
    setDetailForm({
      name: detail.name,
      value: detail.value,
      sort: detail.sort,
      status: detail.status,
      remark: detail.remark || ''
    });
    setShowDetailModal(true);
  };

  const handleDeleteDetail = (detailId: string, projectId: string) => {
    setDeleteTarget({ type: 'detail', id: detailId, projectId });
    setShowDeleteModal(true);
  };

  // 保存项目
  const handleSaveProject = () => {
    if (!projectForm.name.trim() || !projectForm.code.trim()) {
      alert('请填写项目名称和代码');
      return;
    }

    // 检查代码重复
    const existingProject = projects.find(p =>
      p.code === projectForm.code &&
      (!editingProject || p.id !== editingProject.id)
    );
    if (existingProject) {
      alert('项目代码已存在');
      return;
    }

    if (editingProject) {
      // 编辑项目
      setProjects(projects.map(p =>
        p.id === editingProject.id
          ? { ...p, ...projectForm }
          : p
      ));
      alert('项目更新成功！');
    } else {
      // 新增项目
      const newProject: ProjectItem = {
        id: `P${String(projects.length + 1).padStart(3, '0')}`,
        ...projectForm,
        createTime: new Date().toISOString().split('T')[0],
        details: []
      };
      setProjects([...projects, newProject]);
      alert('项目添加成功！');
    }

    setShowProjectModal(false);
    setEditingProject(null);
  };

  // 保存明细
  const handleSaveDetail = () => {
    if (!detailForm.name.trim() || !detailForm.value.trim()) {
      alert('请填写明细名称和值');
      return;
    }

    const project = projects.find(p => p.id === currentProjectId);
    if (!project) return;

    // 检查名称重复
    const existingDetail = project.details.find(d =>
      d.name === detailForm.name &&
      (!editingDetail || d.id !== editingDetail.id)
    );
    if (existingDetail) {
      alert('明细名称已存在');
      return;
    }

    if (editingDetail) {
      // 编辑明细
      setProjects(projects.map(p =>
        p.id === currentProjectId
          ? {
              ...p,
              details: p.details.map(d =>
                d.id === editingDetail.id
                  ? { ...d, ...detailForm }
                  : d
              )
            }
          : p
      ));
      alert('明细更新成功！');
    } else {
      // 新增明细
      const newDetail: DetailItem = {
        id: `D${String(Date.now()).slice(-3)}`,
        ...detailForm
      };
      setProjects(projects.map(p =>
        p.id === currentProjectId
          ? { ...p, details: [...p.details, newDetail] }
          : p
      ));
      alert('明细添加成功！');
    }

    setShowDetailModal(false);
    setEditingDetail(null);
  };

  // 确认删除
  const handleConfirmDelete = () => {
    if (!deleteTarget) return;

    if (deleteTarget.type === 'project') {
      setProjects(projects.filter(p => p.id !== deleteTarget.id));
      alert('项目删除成功！');
    } else if (deleteTarget.type === 'detail' && deleteTarget.projectId) {
      setProjects(projects.map(p =>
        p.id === deleteTarget.projectId
          ? { ...p, details: p.details.filter(d => d.id !== deleteTarget.id) }
          : p
      ));
      alert('明细删除成功！');
    }

    setShowDeleteModal(false);
    setDeleteTarget(null);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          公用代码管理
        </h1>
        <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          管理系统基础配置项目和明细数据
        </p>
      </div>

      {/* 操作区 */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-400" />
            <input
              type="text"
              placeholder="搜索项目名称或代码..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300'
              }`}
            />
          </div>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部类型</option>
            <option value="选项类">选项类</option>
            <option value="时间类">时间类</option>
            <option value="数值类">数值类</option>
          </select>
          
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部状态</option>
            <option value="启用">启用</option>
            <option value="禁用">禁用</option>
          </select>
        </div>
        
        <button
          onClick={handleAddProject}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={16} />
          <span>新增项目</span>
        </button>
      </div>

      {/* 项目列表 */}
      <div className={`rounded-lg border overflow-hidden ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <tr>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  项目信息
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  类型
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  状态
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  明细数量
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  操作
                </th>
              </tr>
            </thead>
            <tbody className={`divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
              {paginatedData.map((project) => (
                <React.Fragment key={project.id}>
                  {/* 项目行 */}
                  <tr className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <button
                          onClick={() => toggleProject(project.id)}
                          className={`mr-2 p-1 rounded hover:bg-gray-200 ${darkMode ? 'hover:bg-gray-600' : ''}`}
                        >
                          {expandedProjects.includes(project.id) ? (
                            <ChevronDown size={16} className={darkMode ? 'text-gray-300' : 'text-gray-600'} />
                          ) : (
                            <ChevronRight size={16} className={darkMode ? 'text-gray-300' : 'text-gray-600'} />
                          )}
                        </button>
                        <div className="flex items-center">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                            project.type === '选项类' ? 'bg-blue-100' :
                            project.type === '时间类' ? 'bg-green-100' : 'bg-purple-100'
                          }`}>
                            {project.type === '选项类' ? (
                              <Settings className={`w-5 h-5 ${
                                project.type === '选项类' ? 'text-blue-600' :
                                project.type === '时间类' ? 'text-green-600' : 'text-purple-600'
                              }`} />
                            ) : project.type === '时间类' ? (
                              <Clock className="w-5 h-5 text-green-600" />
                            ) : (
                              <Utensils className="w-5 h-5 text-purple-600" />
                            )}
                          </div>
                          <div className="ml-4">
                            <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                              {project.name}
                            </div>
                            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {project.code}
                            </div>
                            <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                              {project.description}
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        project.type === '选项类' ? 'bg-blue-100 text-blue-800' :
                        project.type === '时间类' ? 'bg-green-100 text-green-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {project.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        project.status === '启用'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {project.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {project.details.length} 项
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleAddDetail(project.id)}
                          className="text-blue-600 hover:text-blue-900"
                          title="添加明细"
                        >
                          <Plus size={16} />
                        </button>
                        <button
                          onClick={() => handleEditProject(project)}
                          className="text-green-600 hover:text-green-900"
                          title="编辑项目"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteProject(project.id)}
                          className="text-red-600 hover:text-red-900"
                          title="删除项目"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>

                  {/* 明细行 */}
                  {expandedProjects.includes(project.id) && (
                    <tr>
                      <td colSpan={5} className={`px-6 py-2 ${darkMode ? 'bg-gray-750' : 'bg-gray-25'}`}>
                        <div className="ml-8">
                          <div className="grid grid-cols-1 gap-2">
                            {project.details.map((detail) => (
                              <div
                                key={detail.id}
                                className={`flex items-center justify-between p-3 rounded-lg border ${
                                  darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                                }`}
                              >
                                <div className="flex items-center space-x-4">
                                  <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                                    {detail.name}
                                  </div>
                                  <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                    值: {detail.value}
                                  </div>
                                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                    排序: {detail.sort}
                                  </div>
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    detail.status === '启用'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {detail.status}
                                  </span>
                                  {detail.remark && (
                                    <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                                      备注: {detail.remark}
                                    </div>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => handleEditDetail(detail, project.id)}
                                    className="text-green-600 hover:text-green-900"
                                    title="编辑明细"
                                  >
                                    <Edit size={14} />
                                  </button>
                                  <button
                                    onClick={() => handleDeleteDetail(detail.id, project.id)}
                                    className="text-red-600 hover:text-red-900"
                                    title="删除明细"
                                  >
                                    <Trash2 size={14} />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-700'}`}>
            显示 {(currentPage - 1) * pageSize + 1} 到 {Math.min(currentPage * pageSize, filteredProjects.length)} 条，
            共 {filteredProjects.length} 条记录
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded border ${
                currentPage === 1
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-gray-50'
              } ${darkMode ? 'border-gray-600 text-gray-300' : 'border-gray-300'}`}
            >
              上一页
            </button>
            <span className={`px-3 py-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {currentPage} / {totalPages}
            </span>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded border ${
                currentPage === totalPages
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-gray-50'
              } ${darkMode ? 'border-gray-600 text-gray-300' : 'border-gray-300'}`}
            >
              下一页
            </button>
          </div>
        </div>
      )}

      {/* 项目模态框 */}
      {showProjectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`w-full max-w-md mx-4 rounded-lg shadow-xl ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {editingProject ? '编辑项目' : '新增项目'}
              </h3>
            </div>

            <div className="px-6 py-4 space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  项目名称 *
                </label>
                <input
                  type="text"
                  value={projectForm.name}
                  onChange={(e) => setProjectForm({...projectForm, name: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="请输入项目名称"
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  项目代码 *
                </label>
                <input
                  type="text"
                  value={projectForm.code}
                  onChange={(e) => setProjectForm({...projectForm, code: e.target.value.toUpperCase()})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="请输入项目代码"
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  项目类型 *
                </label>
                <select
                  value={projectForm.type}
                  onChange={(e) => setProjectForm({...projectForm, type: e.target.value as any})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="选项类">选项类</option>
                  <option value="时间类">时间类</option>
                  <option value="数值类">数值类</option>
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  项目描述
                </label>
                <textarea
                  value={projectForm.description}
                  onChange={(e) => setProjectForm({...projectForm, description: e.target.value})}
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="请输入项目描述"
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  状态
                </label>
                <select
                  value={projectForm.status}
                  onChange={(e) => setProjectForm({...projectForm, status: e.target.value as any})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="启用">启用</option>
                  <option value="禁用">禁用</option>
                </select>
              </div>
            </div>

            <div className={`px-6 py-4 border-t flex justify-end space-x-3 ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <button
                onClick={() => {
                  setShowProjectModal(false);
                  setEditingProject(null);
                }}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                取消
              </button>
              <button
                onClick={handleSaveProject}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Save size={16} />
                <span>保存</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 明细模态框 */}
      {showDetailModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`w-full max-w-md mx-4 rounded-lg shadow-xl ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {editingDetail ? '编辑明细' : '新增明细'}
              </h3>
            </div>

            <div className="px-6 py-4 space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  明细名称 *
                </label>
                <input
                  type="text"
                  value={detailForm.name}
                  onChange={(e) => setDetailForm({...detailForm, name: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="请输入明细名称"
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  明细值 *
                </label>
                <input
                  type="text"
                  value={detailForm.value}
                  onChange={(e) => setDetailForm({...detailForm, value: e.target.value})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="请输入明细值"
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  排序
                </label>
                <input
                  type="number"
                  value={detailForm.sort}
                  onChange={(e) => setDetailForm({...detailForm, sort: parseInt(e.target.value) || 1})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  min="1"
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  状态
                </label>
                <select
                  value={detailForm.status}
                  onChange={(e) => setDetailForm({...detailForm, status: e.target.value as any})}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="启用">启用</option>
                  <option value="禁用">禁用</option>
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  备注
                </label>
                <textarea
                  value={detailForm.remark}
                  onChange={(e) => setDetailForm({...detailForm, remark: e.target.value})}
                  rows={2}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                  }`}
                  placeholder="请输入备注信息"
                />
              </div>
            </div>

            <div className={`px-6 py-4 border-t flex justify-end space-x-3 ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <button
                onClick={() => {
                  setShowDetailModal(false);
                  setEditingDetail(null);
                }}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                取消
              </button>
              <button
                onClick={handleSaveDetail}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Save size={16} />
                <span>保存</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认模态框 */}
      {showDeleteModal && deleteTarget && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`w-full max-w-md mx-4 rounded-lg shadow-xl ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                确认删除
              </h3>
            </div>

            <div className="px-6 py-4">
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                确定要删除这个{deleteTarget.type === 'project' ? '项目' : '明细'}吗？
                {deleteTarget.type === 'project' && '删除项目将同时删除其下所有明细数据。'}
                此操作不可撤销。
              </p>
            </div>

            <div className={`px-6 py-4 border-t flex justify-end space-x-3 ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeleteTarget(null);
                }}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                取消
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
              >
                <Trash2 size={16} />
                <span>删除</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default PubManage;
