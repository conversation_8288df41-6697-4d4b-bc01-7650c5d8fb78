import { useState } from 'react';
import {
  Search, Plus, Download, Edit, Trash2, Eye, MoreHorizontal, User, Save, AlertTriangle
} from 'lucide-react';

// 用户类型定义
interface UserType {
  id: string;
  name: string;
  username: string;
  role: string;
  phone: string;
  status: string;
  lastLogin: string;
  createTime: string;
  avatar: string;
  permissions: string[];
  password?: string;
}

// 用户数据
const mockUserData = [
  {
    id: 'U001',
    name: '张营养师',
    username: 'zhangnutri',
    role: 'nutritionist',
    phone: '13800138001',
    status: '在职',
    lastLogin: '2024-12-20 14:30:00',
    createTime: '2024-01-10',
    avatar: '/avatars/zhang.jpg',
    permissions: ['膳食管理', '营养分析', '查看报表']
  },
  {
    id: 'U002',
    name: '李员工',
    username: 'listaff',
    role: 'staff',
    phone: '13800138002',
    status: '在职',
    lastLogin: '2024-12-20 13:45:00',
    createTime: '2024-01-15',
    avatar: '/avatars/li.jpg',
    permissions: ['基础操作']
  },
  {
    id: 'U003',
    name: '王营养师',
    username: 'wangnutri',
    role: 'nutritionist',
    phone: '13800138003',
    status: '在职',
    lastLogin: '2024-12-20 15:00:00',
    createTime: '2024-03-10',
    avatar: '/avatars/wang.jpg',
    permissions: ['膳食管理', '营养分析', '查看报表']
  },
  {
    id: 'U004',
    name: '赵管理员',
    username: 'zhaoadmin',
    role: 'admin',
    phone: '13800138004',
    status: '在职',
    lastLogin: '2024-12-20 10:00:00',
    createTime: '2024-01-01',
    avatar: '/avatars/zhao.jpg',
    permissions: ['系统管理', '用户管理', '数据管理', '查看报表']
  },
  {
    id: 'U005',
    name: '陈员工',
    username: 'chenstaff',
    role: 'staff',
    phone: '13800138005',
    status: '离职',
    lastLogin: '2024-11-30 17:30:00',
    createTime: '2024-05-15',
    avatar: '/avatars/chen.jpg',
    permissions: ['基础操作']
  }
];

interface UserManageProps {
  darkMode?: boolean;
}

function UserManage({ darkMode = false }: UserManageProps) {
  // 状态管理
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState('全部');
  const [selectedUserStatus, setSelectedUserStatus] = useState('全部');
  const [currentPageNum, setCurrentPageNum] = useState(1);
  const [pageSize] = useState(5);

  // 模态框状态
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);

  // 用户数据状态
  const [users, setUsers] = useState<UserType[]>(mockUserData);

  // 表单数据
  const [formData, setFormData] = useState<Partial<UserType>>({
    name: '',
    username: '',
    role: '',
    phone: '',
    status: '在职',
    permissions: []
  });

  // 密码重置状态
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const [newPassword, setNewPassword] = useState('');



  // 处理函数
  const handleAddUser = () => {
    setFormData({
      name: '',
      username: '',
      role: '',
      phone: '',
      status: '在职',
      permissions: []
    });
    setShowAddModal(true);
  };

  const handleEditUser = (user: UserType) => {
    setSelectedUser(user);
    setFormData(user);
    setShowEditModal(true);
  };

  const handleViewUser = (user: UserType) => {
    setSelectedUser(user);
    setShowViewModal(true);
  };

  const handleDeleteUser = (user: UserType) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (selectedUser) {
      setUsers(users.filter(user => user.id !== selectedUser.id));
      setShowDeleteModal(false);
      setSelectedUser(null);
      alert('用户删除成功！');
    }
  };



  // 表单验证
  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.name?.trim()) errors.push('姓名不能为空');
    if (!formData.username?.trim()) errors.push('用户名不能为空');
    if (!formData.role) errors.push('请选择角色');
    if (!formData.phone?.trim()) errors.push('电话不能为空');

    // 电话格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (formData.phone && !phoneRegex.test(formData.phone)) {
      errors.push('电话号码格式不正确');
    }

    // 新增用户时密码验证
    if (showAddModal && !formData.password?.trim()) {
      errors.push('密码不能为空');
    }

    // 用户名重复验证
    const existingUser = users.find(user =>
      user.username === formData.username &&
      (!selectedUser || user.id !== selectedUser.id)
    );
    if (existingUser) {
      errors.push('用户名已存在');
    }

    return errors;
  };

  // 根据角色自动分配权限
  const getPermissionsByRole = (role: string): string[] => {
    const rolePermissions: { [key: string]: string[] } = {
      'admin': ['系统管理', '用户管理', '数据管理', '查看报表'],
      'nutritionist': ['膳食管理', '营养分析', '查看报表'],
      'staff': ['基础操作']
    };
    return rolePermissions[role] || ['基础操作'];
  };

  const handleSaveUser = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert('请修正以下错误：\n' + errors.join('\n'));
      return;
    }

    if (showAddModal) {
      // 新增用户
      const newUser: UserType = {
        ...formData as UserType,
        id: `U${String(users.length + 1).padStart(3, '0')}`,
        createTime: new Date().toISOString().split('T')[0],
        lastLogin: '从未登录',
        avatar: '/avatars/default.jpg',
        permissions: getPermissionsByRole(formData.role || '')
      };
      setUsers([...users, newUser]);
      setShowAddModal(false);
      alert('用户添加成功！');
    } else if (showEditModal && selectedUser) {
      // 编辑用户
      const updatedUser = {
        ...formData as UserType,
        id: selectedUser.id,
        createTime: selectedUser.createTime,
        lastLogin: selectedUser.lastLogin,
        avatar: selectedUser.avatar,
        permissions: formData.role !== selectedUser.role
          ? getPermissionsByRole(formData.role || '')
          : selectedUser.permissions
      };

      // 如果重置了密码，添加密码字段
      if (showPasswordReset && newPassword.trim()) {
        updatedUser.password = newPassword;
      }

      setUsers(users.map(user =>
        user.id === selectedUser.id ? updatedUser : user
      ));
      setShowEditModal(false);
      setShowPasswordReset(false);
      setNewPassword('');
      alert(showPasswordReset && newPassword.trim() ? '用户信息和密码更新成功！' : '用户信息更新成功！');
    }

    // 重置表单
    setFormData({
      name: '',
      username: '',
      role: '',
      phone: '',
      status: '在职',
      permissions: []
    });
    setSelectedUser(null);
    setShowPasswordReset(false);
    setNewPassword('');
  };

  const handleExport = () => {
    const dataToExport = filteredUserData.map(user => ({
      用户ID: user.id,
      姓名: user.name,
      用户名: user.username,
      角色: user.role,
      电话: user.phone,
      状态: user.status,
      最后登录: user.lastLogin,
      创建时间: user.createTime,
      权限: user.permissions.join(', ')
    }));

    // 创建CSV内容
    const headers = Object.keys(dataToExport[0] || {});
    const csvContent = [
      headers.join(','),
      ...dataToExport.map(row =>
        headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
      )
    ].join('\n');

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `用户数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert(`成功导出 ${filteredUserData.length} 条用户数据！`);
  };

  // 过滤用户数据
  const filteredUserData = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
                         user.username.toLowerCase().includes(userSearchTerm.toLowerCase());
    const matchesRole = selectedRole === '全部' || user.role === selectedRole;
    const matchesStatus = selectedUserStatus === '全部' || user.status === selectedUserStatus;
    return matchesSearch && matchesRole && matchesStatus;
  });

  // 分页数据
  const totalPages = Math.ceil(filteredUserData.length / pageSize);
  const paginatedData = filteredUserData.slice(
    (currentPageNum - 1) * pageSize,
    currentPageNum * pageSize
  );

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          用户管理
        </h1>
        <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          管理系统用户账户、角色权限和基本信息
        </p>
      </div>

      {/* 操作区 */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-400" />
            <input
              type="text"
              placeholder="搜索用户名或姓名..."
              value={userSearchTerm}
              onChange={(e) => setUserSearchTerm(e.target.value)}
              className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300'
              }`}
            />
          </div>
          
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部角色</option>
            <option value="admin">系统管理员</option>
            <option value="nutritionist">营养师</option>
            <option value="staff">普通员工</option>
          </select>
          
          <select
            value={selectedUserStatus}
            onChange={(e) => setSelectedUserStatus(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部状态</option>
            <option value="在职">在职</option>
            <option value="离职">离职</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleAddUser}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} />
            <span>新增用户</span>
          </button>
          <button
            onClick={handleExport}
            className={`flex items-center space-x-2 px-4 py-2 border rounded-lg transition-colors ${
              darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Download size={16} />
            <span>导出</span>
          </button>
        </div>
      </div>

      {/* 数据表格 */}
      <div className={`rounded-lg border overflow-hidden ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <tr>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  用户信息
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  角色
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  联系方式
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  状态
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  操作
                </th>
              </tr>
            </thead>
            <tbody className={`divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
              {paginatedData.map((user) => (
                <tr key={user.id} className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-white" />
                      </div>
                      <div className="ml-4">
                        <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {user.name}
                        </div>
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          @{user.username}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'admin'
                        ? 'bg-red-100 text-red-800'
                        : user.role === 'nutritionist'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.role === 'admin' ? '系统管理员' : user.role === 'nutritionist' ? '营养师' : '普通员工'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <div className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {user.phone}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.status === '在职'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewUser(user)}
                        className="text-blue-600 hover:text-blue-900"
                        title="查看"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEditUser(user)}
                        className="text-green-600 hover:text-green-900"
                        title="编辑"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user)}
                        className="text-red-600 hover:text-red-900"
                        title="删除"
                      >
                        <Trash2 size={16} />
                      </button>
                      <button className={`${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`} title="更多">
                        <MoreHorizontal size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* 分页 */}
        <div className={`px-6 py-3 border-t flex items-center justify-between ${
          darkMode ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'
        }`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            显示 {(currentPageNum - 1) * pageSize + 1} 到 {Math.min(currentPageNum * pageSize, filteredUserData.length)} 项，共 {filteredUserData.length} 项
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPageNum(Math.max(1, currentPageNum - 1))}
              disabled={currentPageNum === 1}
              className={`px-3 py-1 border rounded-lg transition-colors ${
                currentPageNum === 1 
                  ? (darkMode ? 'border-gray-600 text-gray-500' : 'border-gray-300 text-gray-400')
                  : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
              }`}
            >
              上一页
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPageNum(page)}
                className={`px-3 py-1 border rounded-lg transition-colors ${
                  page === currentPageNum
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => setCurrentPageNum(Math.min(totalPages, currentPageNum + 1))}
              disabled={currentPageNum === totalPages}
              className={`px-3 py-1 border rounded-lg transition-colors ${
                currentPageNum === totalPages 
                  ? (darkMode ? 'border-gray-600 text-gray-500' : 'border-gray-300 text-gray-400')
                  : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
              }`}
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      {/* 新增/编辑用户模态框 */}
      {(showAddModal || showEditModal) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`w-full max-w-2xl mx-4 rounded-lg shadow-xl ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {showAddModal ? '新增用户' : '编辑用户'}
              </h3>
            </div>

            <div className="px-6 py-4 max-h-96 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    姓名 *
                  </label>
                  <input
                    type="text"
                    value={formData.name || ''}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                    placeholder="请输入姓名"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    用户名 *
                  </label>
                  <input
                    type="text"
                    value={formData.username || ''}
                    onChange={(e) => setFormData({...formData, username: e.target.value})}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                    placeholder="请输入用户名"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    角色 *
                  </label>
                  <select
                    value={formData.role || ''}
                    onChange={(e) => setFormData({...formData, role: e.target.value})}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                  >
                    <option value="">请选择角色</option>
                    <option value="admin">系统管理员</option>
                    <option value="nutritionist">营养师</option>
                    <option value="staff">普通员工</option>
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    电话 *
                  </label>
                  <input
                    type="tel"
                    value={formData.phone || ''}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                    placeholder="请输入电话号码"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    状态
                  </label>
                  <select
                    value={formData.status || '在职'}
                    onChange={(e) => setFormData({...formData, status: e.target.value})}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                  >
                    <option value="在职">在职</option>
                    <option value="离职">离职</option>
                  </select>
                </div>

                {showAddModal && (
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      密码 *
                    </label>
                    <input
                      type="password"
                      value={formData.password || ''}
                      onChange={(e) => setFormData({...formData, password: e.target.value})}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                      }`}
                      placeholder="请输入密码"
                    />
                  </div>
                )}

                {showEditModal && (
                  <div className="md:col-span-2">
                    <div className="flex items-center justify-between mb-2">
                      <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        密码管理
                      </label>
                      <button
                        type="button"
                        onClick={() => setShowPasswordReset(!showPasswordReset)}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        {showPasswordReset ? '取消重置' : '重置密码'}
                      </button>
                    </div>
                    {showPasswordReset && (
                      <input
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                        }`}
                        placeholder="请输入新密码"
                      />
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className={`px-6 py-4 border-t flex justify-end space-x-3 ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setShowEditModal(false);
                  setShowPasswordReset(false);
                  setNewPassword('');
                  setFormData({
                    name: '',
                    username: '',
                    role: '',
                    phone: '',
                    status: '在职',
                    permissions: []
                  });
                }}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                取消
              </button>
              <button
                onClick={handleSaveUser}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Save size={16} />
                <span>保存</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 查看用户详情模态框 */}
      {showViewModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`w-full max-w-2xl mx-4 rounded-lg shadow-xl ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                用户详情
              </h3>
            </div>

            <div className="px-6 py-4 max-h-96 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2 flex items-center space-x-4">
                  <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h4 className={`text-xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {selectedUser.name}
                    </h4>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      @{selectedUser.username}
                    </p>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    用户ID
                  </label>
                  <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {selectedUser.id}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    角色
                  </label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    selectedUser.role === 'admin'
                      ? 'bg-red-100 text-red-800'
                      : selectedUser.role === 'nutritionist'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {selectedUser.role === 'admin' ? '系统管理员' : selectedUser.role === 'nutritionist' ? '营养师' : '普通员工'}
                  </span>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    状态
                  </label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    selectedUser.status === '在职'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {selectedUser.status}
                  </span>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    电话
                  </label>
                  <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {selectedUser.phone}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    创建时间
                  </label>
                  <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {selectedUser.createTime}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    最后登录
                  </label>
                  <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {selectedUser.lastLogin}
                  </p>
                </div>

                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    权限列表
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {selectedUser.permissions.map((permission, index) => (
                      <span
                        key={index}
                        className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className={`px-6 py-4 border-t flex justify-end ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setSelectedUser(null);
                }}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认模态框 */}
      {showDeleteModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`w-full max-w-md mx-4 rounded-lg shadow-xl ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                确认删除
              </h3>
            </div>

            <div className="px-6 py-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <p className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    确定要删除用户 "{selectedUser.name}" 吗？
                  </p>
                  <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    此操作不可撤销，用户的所有数据将被永久删除。
                  </p>
                </div>
              </div>
            </div>

            <div className={`px-6 py-4 border-t flex justify-end space-x-3 ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedUser(null);
                }}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                取消
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
              >
                <Trash2 size={16} />
                <span>删除</span>
              </button>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}

export default UserManage;
