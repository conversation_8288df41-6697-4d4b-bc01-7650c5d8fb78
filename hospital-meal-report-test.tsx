import React, { useState, useEffect, useMemo } from 'react';
import {
  Search, FileText, Printer, Calendar, Tag, ArrowLeft, Download, 
  BarChart3, PieChart, TrendingUp, Users, Clock, MapPin,
  Filter, RefreshCw, Eye, Settings, ChevronDown, ChevronRight
} from 'lucide-react';

// 扩展的Mock数据
const mockReportData = [
  {
    id: 'R001',
    bingquid: 'ICU001',
    bingqumc: 'ICU重症监护病区',
    chuanghao: '001',
    bingrenid: 'P001',
    bingrenxm: '张三',
    xingbie: '男',
    nianling: 45,
    zhuyuanriqi: '2024-12-15',
    yishengid: 'D001',
    yishengxm: '李医生',
    ssid: 'F001',
    name: '糖尿病套餐',
    category: '治疗餐',
    dietOrders: ['普食', '糖尿病饮食'],
    shuliang: 1,
    price: 28.5,
    date: '2024-12-25',
    canci: '早餐',
    beizhu: '无糖',
    status: '已配送',
    peisongshijian: '07:30',
    peisongrenid: 'S001',
    peisongrenxm: '王配送员',
    calories: 1800,
    protein: 85,
    fat: 60,
    carbs: 180
  },
  {
    id: 'R002',
    bingquid: 'ICU001',
    bingqumc: 'ICU重症监护病区',
    chuanghao: '001',
    bingrenid: 'P001',
    bingrenxm: '张三',
    xingbie: '男',
    nianling: 45,
    zhuyuanriqi: '2024-12-15',
    yishengid: 'D001',
    yishengxm: '李医生',
    ssid: 'F002',
    name: '清淡粥类',
    category: '流质',
    dietOrders: ['流质'],
    shuliang: 1,
    price: 15.0,
    date: '2024-12-25',
    canci: '午餐',
    beizhu: '',
    status: '已配送',
    peisongshijian: '12:00',
    peisongrenid: 'S001',
    peisongrenxm: '王配送员',
    calories: 800,
    protein: 30,
    fat: 20,
    carbs: 120
  },
  {
    id: 'R003',
    bingquid: 'ICU001',
    bingqumc: 'ICU重症监护病区',
    chuanghao: '002',
    bingrenid: 'P002',
    bingrenxm: '李四',
    xingbie: '女',
    nianling: 32,
    zhuyuanriqi: '2024-12-18',
    yishengid: 'D002',
    yishengxm: '陈医生',
    ssid: 'F003',
    name: '普通营养餐',
    category: '普通餐',
    dietOrders: ['普食'],
    shuliang: 1,
    price: 25.0,
    date: '2024-12-25',
    canci: '早餐',
    beizhu: '',
    status: '配送中',
    peisongshijian: '07:45',
    peisongrenid: 'S002',
    peisongrenxm: '赵配送员',
    calories: 2000,
    protein: 90,
    fat: 70,
    carbs: 250
  },
  {
    id: 'R004',
    bingquid: 'NKBQ001',
    bingqumc: '内科病区一',
    chuanghao: '101',
    bingrenid: 'P003',
    bingrenxm: '王五',
    xingbie: '男',
    nianling: 58,
    zhuyuanriqi: '2024-12-20',
    yishengid: 'D003',
    yishengxm: '刘医生',
    ssid: 'F004',
    name: '低盐营养餐',
    category: '治疗餐',
    dietOrders: ['普食', '低盐饮食'],
    shuliang: 1,
    price: 30.0,
    date: '2024-12-25',
    canci: '早餐',
    beizhu: '少盐',
    status: '已配送',
    peisongshijian: '07:20',
    peisongrenid: 'S001',
    peisongrenxm: '王配送员',
    calories: 1600,
    protein: 75,
    fat: 45,
    carbs: 160
  },
  {
    id: 'R005',
    bingquid: 'NKBQ001',
    bingqumc: '内科病区一',
    chuanghao: '102',
    bingrenid: 'P004',
    bingrenxm: '赵六',
    xingbie: '女',
    nianling: 67,
    zhuyuanriqi: '2024-12-22',
    yishengid: 'D003',
    yishengxm: '刘医生',
    ssid: 'F005',
    name: '软食套餐',
    category: '半流质',
    dietOrders: ['半流质', '软食'],
    shuliang: 1,
    price: 22.0,
    date: '2024-12-25',
    canci: '午餐',
    beizhu: '易消化',
    status: '待配送',
    peisongshijian: '',
    peisongrenid: '',
    peisongrenxm: '',
    calories: 1400,
    protein: 60,
    fat: 45,
    carbs: 180
  },
  {
    id: 'R006',
    bingquid: 'WKBQ001',
    bingqumc: '外科病区一',
    chuanghao: '201',
    bingrenid: 'P005',
    bingrenxm: '孙七',
    xingbie: '男',
    nianling: 41,
    zhuyuanriqi: '2024-12-23',
    yishengid: 'D004',
    yishengxm: '周医生',
    ssid: 'F006',
    name: '高蛋白营养餐',
    category: '治疗餐',
    dietOrders: ['普食', '高蛋白饮食'],
    shuliang: 1,
    price: 35.0,
    date: '2024-12-25',
    canci: '早餐',
    beizhu: '术后恢复',
    status: '已配送',
    peisongshijian: '07:35',
    peisongrenid: 'S003',
    peisongrenxm: '钱配送员',
    calories: 2200,
    protein: 120,
    fat: 80,
    carbs: 200
  }
];

interface ReportFilters {
  bingqu: string;
  date: string;
  canci: string;
  category: string;
  status: string;
  yisheng: string;
  peisongrenxm: string;
}

interface HospitalMealReportTestProps {
  darkMode?: boolean;
}

const HospitalMealReportTest: React.FC<HospitalMealReportTestProps> = ({ darkMode = false }) => {
  const [allData] = useState(mockReportData);
  const [filteredData, setFilteredData] = useState(mockReportData);
  const [viewMode, setViewMode] = useState<'table' | 'cards' | 'summary' | 'analytics'>('table');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>(['basic']);
  
  const [filters, setFilters] = useState<ReportFilters>({
    bingqu: '',
    date: '2024-12-25',
    canci: '',
    category: '',
    status: '',
    yisheng: '',
    peisongrenxm: ''
  });

  // 获取筛选选项
  const filterOptions = useMemo(() => ({
    bingquOptions: [...new Set(allData.map(item => item.bingqumc))],
    canciOptions: ['早餐', '午餐', '晚餐'],
    categoryOptions: [...new Set(allData.map(item => item.category))],
    statusOptions: [...new Set(allData.map(item => item.status))],
    yishengOptions: [...new Set(allData.map(item => item.yishengxm))],
    peisongrenOptions: [...new Set(allData.map(item => item.peisongrenxm).filter(Boolean))]
  }), [allData]);

  // 过滤数据
  useEffect(() => {
    let filtered = allData.filter(item => {
      return (!filters.bingqu || item.bingqumc === filters.bingqu) &&
             (!filters.date || item.date === filters.date) &&
             (!filters.canci || item.canci === filters.canci) &&
             (!filters.category || item.category === filters.category) &&
             (!filters.status || item.status === filters.status) &&
             (!filters.yisheng || item.yishengxm === filters.yisheng) &&
             (!filters.peisongrenxm || item.peisongrenxm === filters.peisongrenxm);
    });
    setFilteredData(filtered);
  }, [filters, allData]);

  // 统计数据
  const statistics = useMemo(() => {
    const totalMeals = filteredData.length;
    const totalPatients = new Set(filteredData.map(item => item.bingrenid)).size;
    const totalWards = new Set(filteredData.map(item => item.bingqumc)).size;
    const totalRevenue = filteredData.reduce((sum, item) => sum + item.price, 0);
    
    const statusStats = filteredData.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const categoryStats = filteredData.reduce((acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const canciStats = filteredData.reduce((acc, item) => {
      acc[item.canci] = (acc[item.canci] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalMeals,
      totalPatients,
      totalWards,
      totalRevenue,
      statusStats,
      categoryStats,
      canciStats
    };
  }, [filteredData]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    const csvContent = [
      ['病区', '床号', '患者姓名', '膳食名称', '分类', '数量', '价格', '日期', '餐次', '状态', '配送时间', '配送员'].join(','),
      ...filteredData.map(item => [
        item.bingqumc,
        item.chuanghao,
        item.bingrenxm,
        item.name,
        item.category,
        item.shuliang,
        item.price,
        item.date,
        item.canci,
        item.status,
        item.peisongshijian,
        item.peisongrenxm
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `送餐报表_${filters.date}.csv`;
    link.click();
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* 页面标题和操作区 */}
      <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} p-6 shadow-sm border-b print:hidden`}>
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2 flex items-center`}>
              <BarChart3 className="mr-3 text-blue-600" size={32} />
              智能送餐报表系统
            </h1>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              全面的膳食配送数据分析与报表管理
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                  viewMode === 'table'
                    ? 'bg-blue-600 text-white'
                    : (darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300')
                }`}
              >
                表格视图
              </button>
              <button
                onClick={() => setViewMode('cards')}
                className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                  viewMode === 'cards'
                    ? 'bg-blue-600 text-white'
                    : (darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300')
                }`}
              >
                卡片视图
              </button>
              <button
                onClick={() => setViewMode('analytics')}
                className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                  viewMode === 'analytics'
                    ? 'bg-blue-600 text-white'
                    : (darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300')
                }`}
              >
                数据分析
              </button>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={handleExport}
                className={`flex items-center px-4 py-2 rounded-lg text-sm transition-colors ${
                  darkMode ? 'bg-green-600 hover:bg-green-700' : 'bg-green-600 hover:bg-green-700'
                } text-white`}
              >
                <Download size={16} className="mr-2" />
                导出数据
              </button>
              <button
                onClick={handlePrint}
                className={`flex items-center px-4 py-2 rounded-lg text-sm transition-colors ${
                  darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-600 hover:bg-blue-700'
                } text-white`}
              >
                <Printer size={16} className="mr-2" />
                打印报表
              </button>
            </div>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className={`${darkMode ? 'bg-gray-700' : 'bg-blue-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-600' : 'border-blue-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-blue-600'}`}>总订餐数</p>
                <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-blue-900'}`}>{statistics.totalMeals}</p>
              </div>
              <Users className={`${darkMode ? 'text-blue-400' : 'text-blue-600'}`} size={24} />
            </div>
          </div>

          <div className={`${darkMode ? 'bg-gray-700' : 'bg-green-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-600' : 'border-green-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-green-600'}`}>患者人数</p>
                <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-green-900'}`}>{statistics.totalPatients}</p>
              </div>
              <MapPin className={`${darkMode ? 'text-green-400' : 'text-green-600'}`} size={24} />
            </div>
          </div>

          <div className={`${darkMode ? 'bg-gray-700' : 'bg-purple-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-600' : 'border-purple-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-purple-600'}`}>涉及病区</p>
                <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-purple-900'}`}>{statistics.totalWards}</p>
              </div>
              <MapPin className={`${darkMode ? 'text-purple-400' : 'text-purple-600'}`} size={24} />
            </div>
          </div>

          <div className={`${darkMode ? 'bg-gray-700' : 'bg-orange-50'} p-4 rounded-lg border ${darkMode ? 'border-gray-600' : 'border-orange-200'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-orange-600'}`}>总金额</p>
                <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-orange-900'}`}>¥{statistics.totalRevenue.toFixed(2)}</p>
              </div>
              <TrendingUp className={`${darkMode ? 'text-orange-400' : 'text-orange-600'}`} size={24} />
            </div>
          </div>
        </div>

        {/* 筛选区域 */}
        <div className={`${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} p-4 rounded-lg border`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Filter size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
              <h3 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>数据筛选</h3>
            </div>
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className={`flex items-center space-x-2 px-3 py-1 rounded-lg text-sm transition-colors ${
                darkMode ? 'bg-gray-600 hover:bg-gray-500 text-gray-300' : 'bg-white hover:bg-gray-100 text-gray-700'
              }`}
            >
              <Settings size={16} />
              <span>高级筛选</span>
              <ChevronDown size={16} className={`transform transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>

          {/* 基础筛选 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>病区</label>
              <select
                value={filters.bingqu}
                onChange={(e) => setFilters({...filters, bingqu: e.target.value})}
                className={`w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300'
                }`}
              >
                <option value="">全部病区</option>
                {filterOptions.bingquOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>日期</label>
              <input
                type="date"
                value={filters.date}
                onChange={(e) => setFilters({...filters, date: e.target.value})}
                className={`w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300'
                }`}
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>餐次</label>
              <select
                value={filters.canci}
                onChange={(e) => setFilters({...filters, canci: e.target.value})}
                className={`w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300'
                }`}
              >
                <option value="">全部餐次</option>
                {filterOptions.canciOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>配送状态</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters({...filters, status: e.target.value})}
                className={`w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300'
                }`}
              >
                <option value="">全部状态</option>
                {filterOptions.statusOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
          </div>

          {/* 高级筛选 */}
          {showAdvancedFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-300 dark:border-gray-600">
              <div>
                <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>膳食分类</label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters({...filters, category: e.target.value})}
                  className={`w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="">全部分类</option>
                  {filterOptions.categoryOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>主治医生</label>
                <select
                  value={filters.yisheng}
                  onChange={(e) => setFilters({...filters, yisheng: e.target.value})}
                  className={`w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="">全部医生</option>
                  {filterOptions.yishengOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>配送员</label>
                <select
                  value={filters.peisongrenxm}
                  onChange={(e) => setFilters({...filters, peisongrenxm: e.target.value})}
                  className={`w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="">全部配送员</option>
                  {filterOptions.peisongrenOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="p-6 print:p-0">
        {viewMode === 'table' && (
          <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border overflow-hidden`}>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <tr>
                    <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                      病区/床号
                    </th>
                    <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                      患者信息
                    </th>
                    <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                      膳食详情
                    </th>
                    <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                      营养信息
                    </th>
                    <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                      配送状态
                    </th>
                    <th className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className={`divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                  {filteredData.map((item) => (
                    <tr key={item.id} className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div>
                          <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {item.bingqumc}
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {item.chuanghao}床
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div>
                          <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {item.bingrenxm}
                          </div>
                          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {item.xingbie} · {item.nianling}岁
                          </div>
                          <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                            主治: {item.yishengxm}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div>
                          <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {item.name}
                          </div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              item.category === '治疗餐'
                                ? 'bg-red-100 text-red-800'
                                : item.category === '流质' || item.category === '半流质'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {item.category}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {item.dietOrders.map((order, index) => (
                              <span key={index} className={`inline-flex px-2 py-1 text-xs rounded ${
                                darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-600'
                              }`}>
                                {order}
                              </span>
                            ))}
                          </div>
                          <div className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {item.canci} · ¥{item.price} · {item.date}
                          </div>
                          {item.beizhu && (
                            <div className={`text-xs mt-1 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                              备注: {item.beizhu}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-xs space-y-1">
                          <div className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                            热量: {item.calories}kcal
                          </div>
                          <div className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                            蛋白质: {item.protein}g
                          </div>
                          <div className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                            脂肪: {item.fat}g
                          </div>
                          <div className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                            碳水: {item.carbs}g
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.status === '已配送'
                              ? 'bg-green-100 text-green-800'
                              : item.status === '配送中'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {item.status}
                          </span>
                          {item.peisongshijian && (
                            <div className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              <Clock size={12} className="inline mr-1" />
                              {item.peisongshijian}
                            </div>
                          )}
                          {item.peisongrenxm && (
                            <div className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              配送: {item.peisongrenxm}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          className="text-blue-600 hover:text-blue-900 mr-2"
                          title="查看详情"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          className="text-green-600 hover:text-green-900"
                          title="打印标签"
                        >
                          <Tag size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredData.length === 0 && (
              <div className="text-center py-12">
                <Search className={`mx-auto h-12 w-12 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mb-4`} />
                <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>未找到符合条件的记录</p>
              </div>
            )}
          </div>
        )}

        {viewMode === 'cards' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredData.map((item) => (
              <div key={item.id} className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6 hover:shadow-lg transition-shadow`}>
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {item.bingrenxm}
                    </h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {item.bingqumc} · {item.chuanghao}床
                    </p>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    item.status === '已配送'
                      ? 'bg-green-100 text-green-800'
                      : item.status === '配送中'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {item.status}
                  </span>
                </div>

                <div className="space-y-3">
                  <div>
                    <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                      膳食信息
                    </h4>
                    <p className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {item.name}
                    </p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        item.category === '治疗餐'
                          ? 'bg-red-100 text-red-800'
                          : item.category === '流质' || item.category === '半流质'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {item.category}
                      </span>
                    </div>
                  </div>

                  <div>
                    <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                      饮食医嘱
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {item.dietOrders.map((order, index) => (
                        <span key={index} className={`inline-flex px-2 py-1 text-xs rounded ${
                          darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-600'
                        }`}>
                          {order}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                        餐次时间
                      </h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {item.canci} · {item.date}
                      </p>
                    </div>
                    <div>
                      <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                        价格
                      </h4>
                      <p className={`text-sm font-semibold ${darkMode ? 'text-green-400' : 'text-green-600'}`}>
                        ¥{item.price}
                      </p>
                    </div>
                  </div>

                  {item.peisongrenxm && (
                    <div>
                      <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                        配送信息
                      </h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {item.peisongrenxm} · {item.peisongshijian}
                      </p>
                    </div>
                  )}

                  {item.beizhu && (
                    <div>
                      <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                        备注
                      </h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {item.beizhu}
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex justify-end space-x-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <button className="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20">
                    <Eye size={16} />
                  </button>
                  <button className="text-green-600 hover:text-green-900 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20">
                    <Tag size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {viewMode === 'analytics' && (
          <div className="space-y-6">
            {/* 状态分布 */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
              <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                配送状态分布
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(statistics.statusStats).map(([status, count]) => (
                  <div key={status} className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                    <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {count}
                    </div>
                    <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {status}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                      占比 {((count / filteredData.length) * 100).toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 膳食分类分布 */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
              <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                膳食分类分布
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {Object.entries(statistics.categoryStats).map(([category, count]) => (
                  <div key={category} className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                    <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {count}
                    </div>
                    <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {category}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                      占比 {((count / filteredData.length) * 100).toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 餐次分布 */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg border p-6`}>
              <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                餐次分布
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(statistics.canciStats).map(([canci, count]) => (
                  <div key={canci} className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-lg`}>
                    <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {count}
                    </div>
                    <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {canci}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                      占比 {((count / filteredData.length) * 100).toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HospitalMealReportTest;
