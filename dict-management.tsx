import React, { useState, useRef } from 'react';
import {
  Search, Plus, Download, Edit, Trash2, Eye, X, Save, Camera
} from 'lucide-react';

// 膳食分类数据 (从pub-manage.tsx获取)
const dietCategories = [
  { id: 'D004', name: '普通餐', value: 'normal', sort: 1, status: '启用', remark: '正常饮食' },
  { id: 'D005', name: '治疗餐', value: 'therapeutic', sort: 2, status: '启用', remark: '特殊治疗饮食' },
  { id: 'D006', name: '流质', value: 'liquid', sort: 3, status: '启用', remark: '流质饮食' },
  { id: 'D007', name: '半流质', value: 'semi_liquid', sort: 4, status: '启用', remark: '半流质饮食' }
];

// 饮食医嘱数据 (用于病人饮食过滤匹配)
const dietOrders = [
  { id: 'Y001', name: '普食', code: 'NORMAL', sort: 1, status: '启用', remark: '正常饮食，无特殊限制' },
  { id: 'Y002', name: '流质', code: 'LIQUID', sort: 2, status: '启用', remark: '流质饮食，适合术后或消化不良患者' },
  { id: 'Y003', name: '半流质', code: 'SEMI_LIQUID', sort: 3, status: '启用', remark: '半流质饮食，过渡期饮食' },
  { id: 'Y004', name: '糖尿病饮食', code: 'DIABETES', sort: 4, status: '启用', remark: '适合糖尿病患者的低糖饮食' },
  { id: 'Y005', name: '低盐饮食', code: 'LOW_SALT', sort: 5, status: '启用', remark: '适合高血压患者的低盐饮食' },
  { id: 'Y006', name: '低脂饮食', code: 'LOW_FAT', sort: 6, status: '启用', remark: '适合心血管疾病患者的低脂饮食' },
  { id: 'Y007', name: '高蛋白饮食', code: 'HIGH_PROTEIN', sort: 7, status: '启用', remark: '适合营养不良或术后恢复患者' },
  { id: 'Y008', name: '儿童饮食', code: 'CHILDREN', sort: 8, status: '启用', remark: '适合儿童的营养配比饮食' },
  { id: 'Y009', name: '素食', code: 'VEGETARIAN', sort: 9, status: '启用', remark: '纯素食饮食' },
  { id: 'Y010', name: '软食', code: 'SOFT', sort: 10, status: '启用', remark: '质地较软的饮食，适合咀嚼困难患者' }
];

// 数据类型定义
interface DietItem {
  id: string;
  name: string;
  category: string;  // 膳食分类：普通餐、治疗餐等
  price: number;
  image: string;
  dietOrders: string[];  // 饮食医嘱ID数组
  dietOrderNames: string[];  // 饮食医嘱名称数组
  sort: number;
  status: '启用' | '停用';
  description: string;
  calories: number;
  protein: number;
  fat: number;
  carbs: number;
  createTime: string;
  updateTime: string;
}

// Mock数据 - 膳食字典数据
const mockDietData = [
  {
    id: 'F001',
    name: '糖尿病套餐',
    category: '治疗餐',
    price: 28.5,
    image: '/images/diabetes-meal.jpg',
    dietOrders: ['Y001', 'Y004'],
    dietOrderNames: ['普食', '糖尿病饮食'],
    sort: 1,
    status: '启用' as '启用' | '停用',
    description: '专为糖尿病患者设计的营养均衡膳食',
    calories: 1800,
    protein: 85,
    fat: 60,
    carbs: 180,
    createTime: '2024-01-15',
    updateTime: '2024-12-20'
  },
  {
    id: 'F002',
    name: '高血压营养餐',
    category: '治疗餐',
    price: 32.0,
    image: '/images/hypertension-meal.jpg',
    dietOrders: ['Y001', 'Y005'],
    dietOrderNames: ['普食', '低盐饮食'],
    sort: 2,
    status: '启用' as '启用' | '停用',
    description: '低盐低脂，适合高血压患者',
    calories: 1600,
    protein: 75,
    fat: 45,
    carbs: 160,
    createTime: '2024-01-20',
    updateTime: '2024-12-18'
  },
  {
    id: 'F003',
    name: '术后流质餐',
    category: '流质',
    price: 18.0,
    image: '/images/liquid-meal.jpg',
    dietOrders: ['Y002'],
    dietOrderNames: ['流质'],
    sort: 3,
    status: '启用' as '启用' | '停用',
    description: '适合术后恢复期患者',
    calories: 800,
    protein: 30,
    fat: 20,
    carbs: 120,
    createTime: '2024-02-01',
    updateTime: '2024-12-15'
  },
  {
    id: 'F004',
    name: '普通营养餐',
    category: '普通餐',
    price: 25.0,
    image: '/images/normal-meal.jpg',
    dietOrders: ['Y001'],
    dietOrderNames: ['普食'],
    sort: 4,
    status: '启用' as '启用' | '停用',
    description: '营养均衡的普通膳食',
    calories: 2000,
    protein: 90,
    fat: 70,
    carbs: 250,
    createTime: '2024-02-10',
    updateTime: '2024-12-10'
  },
  {
    id: 'F005',
    name: '儿童营养餐',
    category: '普通餐',
    price: 22.0,
    image: '/images/children-meal.jpg',
    dietOrders: ['Y001', 'Y008'],
    dietOrderNames: ['普食', '儿童饮食'],
    sort: 5,
    status: '启用' as '启用' | '停用',
    description: '专为儿童设计的营养膳食',
    calories: 1400,
    protein: 60,
    fat: 45,
    carbs: 180,
    createTime: '2024-02-15',
    updateTime: '2024-12-08'
  },
  {
    id: 'F006',
    name: '素食营养餐',
    category: '普通餐',
    price: 20.0,
    image: '/images/vegetarian-meal.jpg',
    dietOrders: ['Y001', 'Y009'],
    dietOrderNames: ['普食', '素食'],
    sort: 6,
    status: '停用' as '启用' | '停用',
    description: '纯素食营养膳食',
    calories: 1600,
    protein: 50,
    fat: 40,
    carbs: 220,
    createTime: '2024-03-01',
    updateTime: '2024-11-30'
  },
  {
    id: 'F007',
    name: '减脂营养餐',
    category: '治疗餐',
    price: 30.0,
    image: '/images/diet-meal.jpg',
    dietOrders: ['Y001', 'Y006'],
    dietOrderNames: ['普食', '低脂饮食'],
    sort: 7,
    status: '启用' as '启用' | '停用',
    description: '低热量减脂膳食',
    calories: 1200,
    protein: 80,
    fat: 30,
    carbs: 120,
    createTime: '2024-03-10',
    updateTime: '2024-12-05'
  },
  {
    id: 'F008',
    name: '高蛋白营养餐',
    category: '治疗餐',
    price: 35.0,
    image: '/images/protein-meal.jpg',
    dietOrders: ['Y001', 'Y007'],
    dietOrderNames: ['普食', '高蛋白饮食'],
    sort: 8,
    status: '启用' as '启用' | '停用',
    description: '高蛋白质含量膳食',
    calories: 2200,
    protein: 120,
    fat: 80,
    carbs: 200,
    createTime: '2024-03-20',
    updateTime: '2024-12-01'
  }
];

interface DictManagementProps {
  darkMode?: boolean;
}

const DictManagement: React.FC<DictManagementProps> = ({ darkMode = false }) => {
  // 状态管理
  const [dietData, setDietData] = useState<DietItem[]>(mockDietData);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [selectedStatus, setSelectedStatus] = useState('全部');
  const [currentPageNum, setCurrentPageNum] = useState(1);
  const [pageSize] = useState(5);

  // 模态框状态
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingItem, setEditingItem] = useState<DietItem | null>(null);
  const [viewingItem, setViewingItem] = useState<DietItem | null>(null);
  const [deleteItemId, setDeleteItemId] = useState<string>('');

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    category: '普通餐',
    price: 0,
    image: '',
    dietOrderNames: [] as string[],
    sort: 1,
    status: '启用' as '启用' | '停用',
    description: '',
    calories: 0,
    protein: 0,
    fat: 0,
    carbs: 0
  });

  // 图片上传相关
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [imagePreview, setImagePreview] = useState<string>('');

  // 图片上传处理
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        setFormData(prev => ({ ...prev, image: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      category: '普通餐',
      price: 0,
      image: '',
      dietOrderNames: [],
      sort: 1,
      status: '启用',
      description: '',
      calories: 0,
      protein: 0,
      fat: 0,
      carbs: 0
    });
    setImagePreview('');
  };

  // 新增膳食
  const handleAdd = () => {
    resetForm();
    setShowAddModal(true);
  };

  // 编辑膳食
  const handleEdit = (item: DietItem) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      category: item.category,
      price: item.price,
      image: item.image,
      dietOrderNames: item.dietOrderNames,
      sort: item.sort,
      status: item.status,
      description: item.description,
      calories: item.calories,
      protein: item.protein,
      fat: item.fat,
      carbs: item.carbs
    });
    setImagePreview(item.image);
    setShowEditModal(true);
  };

  // 查看膳食详情
  const handleView = (item: DietItem) => {
    setViewingItem(item);
    setShowViewModal(true);
  };

  // 删除膳食
  const handleDelete = (id: string) => {
    setDeleteItemId(id);
    setShowDeleteModal(true);
  };

  // 保存膳食（新增或编辑）
  const handleSave = () => {
    if (!formData.name.trim()) {
      alert('请输入膳食名称');
      return;
    }
    if (formData.price <= 0) {
      alert('请输入有效的价格');
      return;
    }

    const now = new Date().toISOString().split('T')[0];

    if (editingItem) {
      // 编辑模式
      const updatedItem: DietItem = {
        ...editingItem,
        ...formData,
        dietOrders: formData.dietOrderNames.map((name) => {
          const order = dietOrders.find(o => o.name === name);
          return order ? order.id : '';
        }).filter(id => id !== ''),
        updateTime: now
      };

      setDietData(prev => prev.map(item =>
        item.id === editingItem.id ? updatedItem : item
      ));
      setShowEditModal(false);
      alert('膳食更新成功！');
    } else {
      // 新增模式
      const newItem: DietItem = {
        id: `F${String(dietData.length + 1).padStart(3, '0')}`,
        ...formData,
        dietOrders: formData.dietOrderNames.map((name) => {
          const order = dietOrders.find(o => o.name === name);
          return order ? order.id : '';
        }).filter(id => id !== ''),
        createTime: now,
        updateTime: now
      };

      setDietData(prev => [...prev, newItem]);
      setShowAddModal(false);
      alert('膳食添加成功！');
    }

    resetForm();
    setEditingItem(null);
  };

  // 确认删除
  const handleConfirmDelete = () => {
    setDietData(prev => prev.filter(item => item.id !== deleteItemId));
    setShowDeleteModal(false);
    setDeleteItemId('');
    alert('膳食删除成功！');
  };

  // 导出数据处理
  const handleExport = () => {
    const dataToExport = filteredData.map(item => ({
      膳食编号: item.id,
      膳食名称: item.name,
      分类: item.category,
      价格: item.price,
      饮食医嘱: item.dietOrderNames.join(', '),
      状态: item.status,
      热量: item.calories + 'kcal',
      蛋白质: item.protein + 'g',
      脂肪: item.fat + 'g',
      碳水化合物: item.carbs + 'g'
    }));
    
    // 这里可以添加实际的导出逻辑
    console.log('导出数据:', dataToExport);
    alert('导出功能已触发，请查看控制台');
  };

  // 过滤膳食数据
  const filteredData = dietData.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '全部' || item.category === selectedCategory;
    const matchesStatus = selectedStatus === '全部' || item.status === selectedStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // 分页数据
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const paginatedData = filteredData.slice(
    (currentPageNum - 1) * pageSize,
    currentPageNum * pageSize
  );

  return (
    <div className="space-y-6">
      {/* 页面标题   <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          管理医院膳食信息，包括分类、价格、饮食医嘱等
        </p>  */}
      <div className="mb-6">
        <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          膳食字典管理
        </h1>
       

      </div>

      {/* 操作区 */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-400" />
            <input
              type="text"
              placeholder="搜索膳食名称或分类..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300'
              }`}
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部分类</option>
            {dietCategories.map(cat => (
              <option key={cat.id} value={cat.name}>{cat.name}</option>
            ))}
          </select>
          
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部状态</option>
            <option value="启用">启用</option>
            <option value="停用">停用</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleAdd}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} />
            <span>新增膳食</span>
          </button>
          <button 
            onClick={handleExport}
            className={`flex items-center space-x-2 px-4 py-2 border rounded-lg transition-colors ${
              darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Download size={16} />
            <span>导出</span>
          </button>
        </div>
      </div>

      {/* 数据表格 */}
      <div className={`rounded-lg border overflow-hidden ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <tr>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  膳食信息
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  分类
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  价格
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  饮食医嘱
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  状态
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  操作
                </th>
              </tr>
            </thead>
            <tbody className={`divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
              {paginatedData.map((item) => (
                <tr key={item.id} className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center">
                        <span className="text-xs text-gray-600">图片</span>
                      </div>
                      <div className="ml-4">
                        <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {item.name}
                        </div>
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          ID: {item.id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      item.category === '治疗餐'
                        ? 'bg-red-100 text-red-800'
                        : item.category === '流质' || item.category === '半流质'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {item.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      ¥{item.price}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {item.dietOrderNames.map((orderName, index) => (
                        <span key={index} className={`inline-flex px-2 py-1 text-xs rounded ${
                          darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-600'
                        }`}>
                          {orderName}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      item.status === '启用'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleView(item)}
                        className="text-blue-600 hover:text-blue-900"
                        title="查看"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEdit(item)}
                        className="text-green-600 hover:text-green-900"
                        title="编辑"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(item.id)}
                        className="text-red-600 hover:text-red-900"
                        title="删除"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        <div className={`px-6 py-3 border-t flex items-center justify-between ${
          darkMode ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'
        }`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            显示 {(currentPageNum - 1) * pageSize + 1} 到 {Math.min(currentPageNum * pageSize, filteredData.length)} 项，共 {filteredData.length} 项
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPageNum(Math.max(1, currentPageNum - 1))}
              disabled={currentPageNum === 1}
              className={`px-3 py-1 border rounded-lg transition-colors ${
                currentPageNum === 1
                  ? (darkMode ? 'border-gray-600 text-gray-500' : 'border-gray-300 text-gray-400')
                  : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
              }`}
            >
              上一页
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPageNum(page)}
                className={`px-3 py-1 border rounded-lg transition-colors ${
                  page === currentPageNum
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => setCurrentPageNum(Math.min(totalPages, currentPageNum + 1))}
              disabled={currentPageNum === totalPages}
              className={`px-3 py-1 border rounded-lg transition-colors ${
                currentPageNum === totalPages
                  ? (darkMode ? 'border-gray-600 text-gray-500' : 'border-gray-300 text-gray-400')
                  : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
              }`}
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      {/* 新增/编辑模态框 */}
      {(showAddModal || showEditModal) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex justify-between items-center mb-4">
              <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {editingItem ? '编辑膳食' : '新增膳食'}
              </h2>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setShowEditModal(false);
                  resetForm();
                }}
                className={`p-2 rounded-lg hover:bg-gray-100 ${darkMode ? 'hover:bg-gray-700' : ''}`}
              >
                <X size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 基本信息 */}
              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    膳食名称 *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                    placeholder="请输入膳食名称"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    膳食分类
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                  >
                    {dietCategories.map(cat => (
                      <option key={cat.id} value={cat.name}>{cat.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    价格 *
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={formData.price}
                    onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                    placeholder="请输入价格"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    排序
                  </label>
                  <input
                    type="number"
                    value={formData.sort}
                    onChange={(e) => setFormData(prev => ({ ...prev, sort: parseInt(e.target.value) || 1 }))}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    状态
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as '启用' | '停用' }))}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                    }`}
                  >
                    <option value="启用">启用</option>
                    <option value="停用">停用</option>
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    饮食医嘱 *
                  </label>
                  <div className={`border rounded-lg p-3 max-h-32 overflow-y-auto ${
                    darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-white'
                  }`}>
                    {dietOrders.map(order => (
                      <label key={order.id} className="flex items-center space-x-2 mb-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.dietOrderNames.includes(order.name)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                dietOrderNames: [...prev.dietOrderNames, order.name]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                dietOrderNames: prev.dietOrderNames.filter(name => name !== order.name)
                              }));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {order.name}
                        </span>
                        <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          ({order.remark})
                        </span>
                      </label>
                    ))}
                  </div>
                  <div className="mt-1">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      已选择: {formData.dietOrderNames.length} 项
                    </span>
                  </div>
                </div>
              </div>

              {/* 图片和营养信息 */}
              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    膳食图片
                  </label>
                  <div className="space-y-2">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className={`w-full px-3 py-2 border-2 border-dashed rounded-lg hover:bg-gray-50 transition-colors ${
                        darkMode ? 'border-gray-600 hover:bg-gray-700' : 'border-gray-300'
                      }`}
                    >
                      <Camera size={24} className={`mx-auto mb-2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                      <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        点击上传图片
                      </span>
                    </button>
                    {imagePreview && (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="预览"
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <button
                          onClick={() => {
                            setImagePreview('');
                            setFormData(prev => ({ ...prev, image: '' }));
                          }}
                          className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      热量(kcal)
                    </label>
                    <input
                      type="number"
                      value={formData.calories}
                      onChange={(e) => setFormData(prev => ({ ...prev, calories: parseInt(e.target.value) || 0 }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                      }`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      蛋白质(g)
                    </label>
                    <input
                      type="number"
                      value={formData.protein}
                      onChange={(e) => setFormData(prev => ({ ...prev, protein: parseInt(e.target.value) || 0 }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                      }`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      脂肪(g)
                    </label>
                    <input
                      type="number"
                      value={formData.fat}
                      onChange={(e) => setFormData(prev => ({ ...prev, fat: parseInt(e.target.value) || 0 }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                      }`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      碳水化合物(g)
                    </label>
                    <input
                      type="number"
                      value={formData.carbs}
                      onChange={(e) => setFormData(prev => ({ ...prev, carbs: parseInt(e.target.value) || 0 }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                      }`}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4">
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                膳食描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
                }`}
                placeholder="请输入膳食描述"
              />
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setShowEditModal(false);
                  resetForm();
                }}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                取消
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Save size={16} />
                <span>保存</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 查看详情模态框 */}
      {showViewModal && viewingItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex justify-between items-center mb-4">
              <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                膳食详情
              </h2>
              <button
                onClick={() => setShowViewModal(false)}
                className={`p-2 rounded-lg hover:bg-gray-100 ${darkMode ? 'hover:bg-gray-700' : ''}`}
              >
                <X size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {viewingItem.name}
                  </h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    ID: {viewingItem.id}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      分类：
                    </span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-2 ${
                      viewingItem.category === '治疗餐'
                        ? 'bg-red-100 text-red-800'
                        : viewingItem.category === '流质' || viewingItem.category === '半流质'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {viewingItem.category}
                    </span>
                  </div>
                  <div>
                    <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      价格：
                    </span>
                    <span className={`text-sm font-bold text-green-600 ml-2`}>
                      ¥{viewingItem.price}
                    </span>
                  </div>
                  <div>
                    <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      状态：
                    </span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-2 ${
                      viewingItem.status === '启用'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {viewingItem.status}
                    </span>
                  </div>
                  <div>
                    <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      排序：
                    </span>
                    <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'} ml-2`}>
                      {viewingItem.sort}
                    </span>
                  </div>
                </div>

                <div>
                  <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    饮食医嘱：
                  </span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {viewingItem.dietOrderNames.map((orderName, index) => (
                      <span key={index} className={`inline-flex px-2 py-1 text-xs rounded ${
                        darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-600'
                      }`}>
                        {orderName}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    描述：
                  </span>
                  <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {viewingItem.description}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                {viewingItem.image && (
                  <div>
                    <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      膳食图片：
                    </span>
                    <img
                      src={viewingItem.image}
                      alt={viewingItem.name}
                      className="w-full h-48 object-cover rounded-lg mt-2"
                    />
                  </div>
                )}

                <div>
                  <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    营养成分：
                  </span>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>热量</div>
                      <div className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {viewingItem.calories}
                      </div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>kcal</div>
                    </div>
                    <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>蛋白质</div>
                      <div className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {viewingItem.protein}
                      </div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>g</div>
                    </div>
                    <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>脂肪</div>
                      <div className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {viewingItem.fat}
                      </div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>g</div>
                    </div>
                    <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>碳水化合物</div>
                      <div className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {viewingItem.carbs}
                      </div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>g</div>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500 space-y-1">
                  <div>创建时间：{viewingItem.createTime}</div>
                  <div>更新时间：{viewingItem.updateTime}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认模态框 */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`bg-white rounded-lg p-6 w-full max-w-md ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <Trash2 size={20} className="text-red-600" />
              </div>
              <h2 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                确认删除
              </h2>
            </div>

            <p className={`text-sm mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              您确定要删除这个膳食吗？此操作不可撤销。
            </p>

            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowDeleteModal(false)}
                className={`px-4 py-2 border rounded-lg transition-colors ${
                  darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                取消
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                确认删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DictManagement;
