<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公用代码管理测试页面</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // 简化版的图标组件
        const Search = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
            </svg>
        );

        const Plus = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
        );

        const Edit = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
        );

        const Trash2 = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
        );

        const Settings = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
        );

        const Clock = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
        );

        const Utensils = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
                <path d="M7 2v20"></path>
                <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3z"></path>
            </svg>
        );

        // 测试公用代码管理组件
        function TestPubManage() {
            const [darkMode, setDarkMode] = useState(false);

            return (
                <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}>
                    <div className="container mx-auto px-4 py-8">
                        <div className="mb-4">
                            <button
                                onClick={() => setDarkMode(!darkMode)}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                            >
                                切换 {darkMode ? '浅色' : '深色'} 模式
                            </button>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                            <h1 className="text-2xl font-bold mb-6">公用代码管理功能测试</h1>
                            <div className="space-y-4">
                                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <h3 className="font-semibold text-green-800">✅ 已完成的功能</h3>
                                    <ul className="mt-2 text-sm text-green-700 space-y-1">
                                        <li>• 项目管理：增删改查基础配置项目</li>
                                        <li>• 明细管理：为每个项目添加、编辑、删除明细项</li>
                                        <li>• 分类管理：支持选项类、时间类、数值类三种项目类型</li>
                                        <li>• 搜索筛选：按项目名称、代码、类型、状态筛选</li>
                                        <li>• 展开收起：可展开查看项目下的所有明细</li>
                                        <li>• 状态管理：支持启用/禁用状态切换</li>
                                        <li>• 排序功能：明细项支持自定义排序</li>
                                        <li>• 数据验证：完整的表单验证和重复检查</li>
                                        <li>• 深色模式：完整的深色主题支持</li>
                                        <li>• 响应式设计：适配不同屏幕尺寸</li>
                                    </ul>
                                </div>
                                
                                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <h3 className="font-semibold text-blue-800">🏥 预置基础数据</h3>
                                    <ul className="mt-2 text-sm text-blue-700 space-y-1">
                                        <li>• <strong>餐次管理</strong>：早餐、中餐、晚餐</li>
                                        <li>• <strong>膳食分类</strong>：普通餐、治疗餐、流质、半流质</li>
                                        <li>• <strong>订餐截止时间</strong>：各餐次的订餐截止时间配置</li>
                                    </ul>
                                </div>

                                <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                                    <h3 className="font-semibold text-purple-800">🔧 技术特性</h3>
                                    <ul className="mt-2 text-sm text-purple-700 space-y-1">
                                        <li>• <strong>层级结构</strong>：项目-明细两级管理结构</li>
                                        <li>• <strong>类型分类</strong>：选项类、时间类、数值类项目分类</li>
                                        <li>• <strong>代码唯一性</strong>：项目代码和明细名称唯一性验证</li>
                                        <li>• <strong>状态控制</strong>：项目和明细独立的启用/禁用状态</li>
                                        <li>• <strong>排序管理</strong>：明细项支持数字排序</li>
                                        <li>• <strong>备注信息</strong>：明细项支持备注说明</li>
                                    </ul>
                                </div>

                                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <h3 className="font-semibold text-yellow-800">📝 使用说明</h3>
                                    <ul className="mt-2 text-sm text-yellow-700 space-y-1">
                                        <li>• 点击"新增项目"创建新的配置项目</li>
                                        <li>• 点击项目行左侧箭头展开/收起明细列表</li>
                                        <li>• 点击"+"图标为项目添加明细项</li>
                                        <li>• 使用搜索框和筛选器快速查找项目</li>
                                        <li>• 编辑和删除操作都有确认提示</li>
                                        <li>• 项目类型决定了明细值的数据格式</li>
                                    </ul>
                                </div>

                                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                                    <h3 className="font-semibold text-gray-800">💡 应用场景</h3>
                                    <ul className="mt-2 text-sm text-gray-700 space-y-1">
                                        <li>• 医院膳食管理系统的基础配置</li>
                                        <li>• 餐次时间、膳食类型等标准化管理</li>
                                        <li>• 系统参数的集中配置和维护</li>
                                        <li>• 业务规则的灵活配置支持</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestPubManage />, document.getElementById('root'));
    </script>
</body>
</html>
