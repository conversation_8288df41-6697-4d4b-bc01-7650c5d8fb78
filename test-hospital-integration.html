<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医院膳食管理系统 - 公用代码管理集成测试</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // 简化版的图标组件
        const Settings = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
        );

        const Code = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="16,18 22,12 16,6"></polyline>
                <polyline points="8,6 2,12 8,18"></polyline>
            </svg>
        );

        const Users = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
        );

        const ChevronDown = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
        );

        const ChevronRight = ({ size = 16, className = "" }) => (
            <svg width={size} height={size} className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
        );

        // 测试医院膳食管理系统集成
        function TestHospitalIntegration() {
            const [darkMode, setDarkMode] = useState(false);
            const [expandedMenus, setExpandedMenus] = useState(['system-management']);

            const toggleMenu = (menuId) => {
                setExpandedMenus(prev =>
                    prev.includes(menuId)
                        ? prev.filter(id => id !== menuId)
                        : [...prev, menuId]
                );
            };

            return (
                <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}>
                    <div className="container mx-auto px-4 py-8">
                        <div className="mb-4">
                            <button
                                onClick={() => setDarkMode(!darkMode)}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                            >
                                切换 {darkMode ? '浅色' : '深色'} 模式
                            </button>
                        </div>
                        
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            {/* 菜单演示 */}
                            <div className={`rounded-lg shadow-lg p-6 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                                <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                                    管理员菜单结构
                                </h2>
                                
                                <div className="space-y-2">
                                    {/* 系统管理菜单 */}
                                    <div>
                                        <button
                                            onClick={() => toggleMenu('system-management')}
                                            className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                                                darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'
                                            }`}
                                        >
                                            <div className="flex items-center space-x-3">
                                                <Settings size={20} />
                                                <span>系统管理</span>
                                            </div>
                                            {expandedMenus.includes('system-management') ? 
                                                <ChevronDown size={16} /> : <ChevronRight size={16} />
                                            }
                                        </button>
                                        
                                        {/* 子菜单 */}
                                        {expandedMenus.includes('system-management') && (
                                            <div className="ml-8 mt-2 space-y-1">
                                                <button className={`w-full text-left px-3 py-2 rounded-lg transition-colors bg-blue-50 text-blue-600 border-l-4 border-blue-600`}>
                                                    公用代码管理 ⭐ 新增
                                                </button>
                                                <button className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                                                    darkMode ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                                                }`}>
                                                    菜单管理
                                                </button>
                                                <button className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                                                    darkMode ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                                                }`}>
                                                    系统日志
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* 快捷操作演示 */}
                            <div className={`rounded-lg shadow-lg p-6 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                                <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                                    管理员快捷操作
                                </h2>
                                
                                <div className="grid grid-cols-1 gap-4">
                                    <button className="p-4 text-center rounded-lg border border-blue-200 hover:bg-blue-50 transition-colors">
                                        <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                                        <span className="text-sm text-blue-600">用户管理</span>
                                    </button>
                                    <button className="p-4 text-center rounded-lg border border-indigo-200 hover:bg-indigo-50 transition-colors bg-indigo-50">
                                        <Code className="w-8 h-8 text-indigo-600 mx-auto mb-2" />
                                        <span className="text-sm text-indigo-600">代码管理 ⭐ 新增</span>
                                    </button>
                                    <button className="p-4 text-center rounded-lg border border-green-200 hover:bg-green-50 transition-colors">
                                        <Settings className="w-8 h-8 text-green-600 mx-auto mb-2" />
                                        <span className="text-sm text-green-600">系统设置</span>
                                    </button>
                                </div>
                            </div>

                            {/* 功能说明 */}
                            <div className={`rounded-lg shadow-lg p-6 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                                <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                                    集成完成
                                </h2>
                                
                                <div className="space-y-4">
                                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                        <h3 className="font-semibold text-green-800">✅ 菜单集成</h3>
                                        <p className="text-sm text-green-700 mt-1">
                                            已在系统管理菜单下添加"公用代码管理"子菜单
                                        </p>
                                    </div>
                                    
                                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                        <h3 className="font-semibold text-blue-800">🔐 权限控制</h3>
                                        <p className="text-sm text-blue-700 mt-1">
                                            仅对管理员角色开放访问权限
                                        </p>
                                    </div>
                                    
                                    <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                                        <h3 className="font-semibold text-purple-800">🚀 快捷访问</h3>
                                        <p className="text-sm text-purple-700 mt-1">
                                            在管理员仪表盘添加快捷操作按钮
                                        </p>
                                    </div>
                                    
                                    <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                        <h3 className="font-semibold text-orange-800">📋 功能完整</h3>
                                        <p className="text-sm text-orange-700 mt-1">
                                            支持餐次、膳食分类、截止时间等基础数据管理
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div className={`mt-6 p-6 rounded-lg shadow-lg ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                            <h2 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                                访问路径
                            </h2>
                            <div className="space-y-2">
                                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                                    <span className={`text-sm font-mono ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                        主菜单 → 系统管理 → 公用代码管理
                                    </span>
                                </div>
                                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                                    <span className={`text-sm font-mono ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                        仪表盘 → 快捷操作 → 代码管理
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestHospitalIntegration />, document.getElementById('root'));
    </script>
</body>
</html>
